import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { FieldValues } from 'react-hook-form';
import { Card, Table, IconCard, Tooltip, Loading, ConfirmDeleteModal } from '@/shared/components/common';
import { TableColumn } from '@/shared/components/common/Table/types';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import ActiveFilters from '@/modules/components/filters/ActiveFilters';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { useFormErrors } from '@/shared/hooks/';
import CustomerForm from '../components/forms/CustomerForm';
import CustomerDetailView from '../components/forms/CustomerDetailView';
import CustomerImport from '../components/import/CustomerImport';
import { useConvertCustomers, useBulkDeleteConvertCustomers } from '../hooks/useCustomerQuery';
import {
  UserConvertCustomerListItemDto,
  QueryUserConvertCustomerDto,
  UserConvertCustomerSortField,
  SortDirection,
} from '../types/customer.types';

/**
 * Trang quản lý khách hàng
 */
const CustomerPage: React.FC = () => {
  const { t } = useTranslation(['business', 'common']);

  // State cho filter và pagination
  const [searchTerm, setSearchTerm] = useState('');
  const [platform, setPlatform] = useState<string>('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // State cho detail view
  const [selectedCustomer, setSelectedCustomer] = useState<UserConvertCustomerListItemDto | null>(
    null
  );

  // State cho bulk delete
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [showBulkDeleteModal, setShowBulkDeleteModal] = useState(false);

  // Query parameters
  const queryParams: QueryUserConvertCustomerDto = useMemo(
    () => ({
      page: currentPage,
      limit: pageSize,
      search: searchTerm || undefined,
      platform: platform || undefined,
      sortBy: UserConvertCustomerSortField.CREATED_AT,
      sortDirection: SortDirection.DESC,
    }),
    [currentPage, pageSize, searchTerm, platform]
  );

  // Fetch data using API
  const { data: customerData, isLoading, error, refetch } = useConvertCustomers(queryParams);

  // Hook để xử lý lỗi form
  const { formRef, setFormErrors } = useFormErrors<FieldValues>();

  // Hook bulk delete
  const bulkDeleteMutation = useBulkDeleteConvertCustomers();

  // Sử dụng hook animation cho form thêm mới
  const {
    isVisible: isAddFormVisible,
    showForm: showAddForm,
    hideForm: hideAddForm,
  } = useSlideForm();

  // Sử dụng hook animation cho detail view
  const { isVisible: isDetailVisible, showForm: showDetail, hideForm: hideDetail } = useSlideForm();

  // Sử dụng hook animation cho import modal
  const { isVisible: isImportVisible, showForm: showImport, hideForm: hideImport } = useSlideForm();

  // Định nghĩa cột cho bảng
  const columns: TableColumn<UserConvertCustomerListItemDto>[] = [
    {
      key: 'avatar',
      title: t('business:customer.form.avatar'),
      dataIndex: 'avatar',
      render: (value: unknown) => {
        const avatarUrl = value as string;
        return avatarUrl ? (
          <img src={avatarUrl} alt="Avatar" className="w-8 h-8 rounded-full object-cover" />
        ) : (
          <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center">
            <span className="text-gray-500 text-xs">N/A</span>
          </div>
        );
      },
    },
    {
      key: 'name',
      title: t('business:common.form.name'),
      dataIndex: 'name',
      sortable: true,
      render: (value: unknown) => (value as string) || 'N/A',
    },
    {
      key: 'email',
      title: t('business:common.form.email'),
      dataIndex: 'email',
      sortable: true,
      render: (value: unknown) => {
        const email = value as { primary?: string } | string | null;
        if (typeof email === 'object' && email !== null) {
          return email.primary || 'N/A';
        }
        return email || 'N/A';
      },
    },
    {
      key: 'phone',
      title: t('business:common.form.phone'),
      dataIndex: 'phone',
      sortable: true,
      render: (value: unknown) => (value as string) || 'N/A',
    },
    {
      key: 'platform',
      title: t('business:customer.platform'),
      dataIndex: 'platform',
      sortable: true,
      render: (value: unknown) => (value as string) || 'N/A',
    },
    {
      key: 'createdAt',
      title: t('common:createdAt'),
      dataIndex: 'createdAt',
      sortable: true,
      render: (value: unknown) => {
        try {
          if (!value) return 'N/A';

          let date: Date;

          if (typeof value === 'number') {
            date = new Date(value);
          } else if (typeof value === 'string') {
            if (/^\d+$/.test(value)) {
              date = new Date(Number(value));
            } else {
              date = new Date(value);
            }
          } else if (value instanceof Date) {
            date = value;
          } else {
            return 'N/A';
          }

          if (isNaN(date.getTime())) {
            return 'N/A';
          }

          return date.toLocaleDateString('vi-VN');
        } catch (error) {
          console.error('Error formatting date:', error, 'Value:', value);
          return 'N/A';
        }
      },
    },
    {
      key: 'actions',
      title: t('common:actions'),
      render: (_, record) => {
        return (
          <div className="flex space-x-2">
            <Tooltip content={t('common:view')}>
              <IconCard
                icon="eye"
                variant="ghost"
                size="sm"
                onClick={() => handleViewCustomer(record)}
              />
            </Tooltip>
          </div>
        );
      },
    },
  ];

  // Xử lý thêm mới
  const handleAdd = () => {
    // Clear form errors khi mở form mới
    setFormErrors({});
    showAddForm();
  };

  // Xử lý xem chi tiết khách hàng
  const handleViewCustomer = (customer: UserConvertCustomerListItemDto) => {
    setSelectedCustomer(customer);
    showDetail();
  };

  // Xử lý thay đổi platform filter
  const handlePlatformFilter = (selectedPlatform: string) => {
    setPlatform(selectedPlatform === 'all' ? '' : selectedPlatform);
    setCurrentPage(1); // Reset về trang đầu khi filter
  };

  // Xử lý clear search
  const handleClearSearch = () => {
    setSearchTerm('');
    setCurrentPage(1);
  };

  // Xử lý clear platform filter
  const handleClearPlatformFilter = () => {
    setPlatform('');
    setCurrentPage(1);
  };

  // Xử lý clear all filters
  const handleClearAllFilters = () => {
    setSearchTerm('');
    setPlatform('');
    setCurrentPage(1);
  };

  // Xử lý khi tạo khách hàng thành công
  const handleCustomerSuccess = () => {
    // Clear form errors và đóng form khi thành công
    setFormErrors({});
    hideAddForm();
  };

  // Xử lý hủy form thêm khách hàng
  const handleCancelAdd = () => {
    // Clear form errors khi hủy form
    setFormErrors({});
    hideAddForm();
  };

  // Xử lý đóng detail view
  const handleCloseDetail = () => {
    setSelectedCustomer(null);
    hideDetail();
  };

  // Xử lý import khách hàng
  const handleImport = () => {
    showImport();
  };

  // TaskQueue sẽ tự động refresh data khi import hoàn thành

  // Xử lý hiển thị modal xác nhận xóa nhiều
  const handleShowBulkDeleteConfirm = () => {
    setShowBulkDeleteModal(true);
  };

  // Xử lý hủy xóa nhiều
  const handleCancelBulkDelete = () => {
    setShowBulkDeleteModal(false);
  };

  // Xử lý xác nhận xóa nhiều
  const handleConfirmBulkDelete = async () => {
    try {
      const customerIds = selectedRowKeys.map(key => Number(key));
      await bulkDeleteMutation.mutateAsync(customerIds);
      setSelectedRowKeys([]);
      setShowBulkDeleteModal(false);
    } catch (error) {
      console.error('Lỗi khi xóa nhiều khách hàng:', error);
    }
  };

  // Xử lý thay đổi selection
  const handleSelectionChange = (selectedKeys: React.Key[]) => {
    setSelectedRowKeys(selectedKeys);
  };

  // Hiển thị loading state
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loading />
      </div>
    );
  }

  // Hiển thị error state
  if (error) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-center">
          <p className="text-red-500 mb-4">Có lỗi xảy ra khi tải dữ liệu</p>
          <button
            onClick={() => refetch()}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Thử lại
          </button>
        </div>
      </div>
    );
  }

  return (
    <div>
      {/* Modal xác nhận xóa nhiều */}
      <ConfirmDeleteModal
        isOpen={showBulkDeleteModal}
        onClose={handleCancelBulkDelete}
        onConfirm={handleConfirmBulkDelete}
        title={t('common:confirmDelete')}
        message={t('business:customer.bulkDeleteConfirmation', {
          count: selectedRowKeys.length,
        })}
        itemName={`${selectedRowKeys.length} khách hàng`}
        isSubmitting={bulkDeleteMutation.isPending}
      />

      <MenuIconBar
        onSearch={setSearchTerm}
        onAdd={handleAdd}
        additionalIcons={[
          {
            tooltip: t('business:customer.import.title'),
            icon: 'upload',
            variant: 'primary',
            onClick: handleImport,
          },
          {
            icon: 'refresh',
            tooltip: 'Làm mới dữ liệu',
            variant: 'primary',
            onClick: () => refetch(),
          },
          {
            icon: 'trash',
            tooltip: t('common:bulkDelete'),
            variant: 'ghost',
            onClick: handleShowBulkDeleteConfirm,
            condition: selectedRowKeys.length > 0,
          },
        ]}
      />

      {/* Active Filters */}
      <ActiveFilters
        searchTerm={searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={platform}
        filterLabel={platform || ''}
        onClearFilter={handleClearPlatformFilter}
        onClearAll={handleClearAllFilters}
      />

      {/* Form thêm khách hàng */}
      <SlideInForm isVisible={isAddFormVisible}>
          <CustomerForm
            formRef={formRef}
            onSuccess={handleCustomerSuccess}
            onCancel={handleCancelAdd}
            title={t('business:customer.addForm')}
          />
      </SlideInForm>

      {/* Detail view khách hàng */}
      <SlideInForm isVisible={isDetailVisible}>
        {selectedCustomer && (
          <CustomerDetailView
            customerId={selectedCustomer.id}
            onClose={handleCloseDetail}
          />
        )}
      </SlideInForm>

      {/* Import form */}
      <SlideInForm isVisible={isImportVisible}>
        <CustomerImport
          onClose={hideImport}
        />
      </SlideInForm>

      <Card className="overflow-hidden">
        <Table
          columns={columns}
          data={customerData?.items || []}
          rowKey="id"
          rowSelection={{
            selectedRowKeys,
            onChange: handleSelectionChange,
          }}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: customerData?.meta?.totalItems || 0,
            showSizeChanger: true,
            onChange: (page: number, size?: number) => {
              setCurrentPage(page);
              if (size && size !== pageSize) {
                setPageSize(size);
              }
            },
          }}
        />
      </Card>
    </div>
  );
};

export default CustomerPage;
